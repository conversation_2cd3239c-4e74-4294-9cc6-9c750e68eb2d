package cn.source.web.controller.system;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/health")
public class HealthController
{
    /**
     * 简单的健康检查端点
     */
    @GetMapping
    public Map<String, Object> health()
    {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        result.put("application", "邻里办小程序API");
        result.put("version", "1.0.0");
        return result;
    }
}
