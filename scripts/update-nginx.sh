#!/bin/bash
set -e

PORT=$1
CONF_TEMPLATE="nginx/mapi.zn.nextv.show.conf.template"
CONF_FILE="mapi.zn.nextv.show.conf"

# 使用环境变量替换模板中的占位符
sed "s/{{PORT}}/$PORT/g" $CONF_TEMPLATE > $CONF_FILE

# 上传到服务器并重载 Nginx
scp -o StrictHostKeyChecking=no $CONF_FILE $DEPLOY_USER@$DEPLOY_HOST:/etc/nginx/conf.d/
ssh -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_HOST "sudo nginx -t && sudo systemctl reload nginx"

echo "Nginx 配置已更新为端口: $PORT"