# 文件路径: .github/workflows/deploy-to-production.yml

name: Deploy Java API to Production with Dynamic Port

on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    env:
      # 应用唯一标识符
      APP_ID: "zn.mapi"
    
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
      
      # 步骤1: 生成动态端口 (使用新方法)
      - name: Generate dynamic port
        id: port-generator
        run: |
          # 生成8000-9000之间的随机端口
          PORT=$(shuf -i 8000-9000 -n 1)
          echo "Generated dynamic port: $PORT"
          
          # 使用新方法设置输出
          echo "port=$PORT" >> $GITHUB_OUTPUT
          echo "DYNAMIC_PORT=$PORT" >> $GITHUB_ENV
      
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          java-version: '8'
          distribution: 'temurin'
          cache: 'maven'

      - name: Run Tests (if present)
        run: |
          if mvn help:describe -Dplugin=org.apache.maven.plugins:maven-surefire-plugin -Ddetail=false -q 2>/dev/null; then
            mvn test
          else
            echo "No test configuration found, skipping tests."
          fi

      - name: Build Application
        run: |
          mvn clean package -DskipTests
          if [ ! -f "source-admin/target/source-admin.jar" ]; then
            echo "Build failed: source-admin.jar not found"
            exit 1
          fi
          echo "Build successful: source-admin.jar created"
      
      # 步骤2: 准备Nginx配置模板
      - name: Prepare Nginx configuration template
        run: |
          # 创建Nginx配置模板，使用{{PORT}}作为占位符
          cat << EOF > nginx-config-template.conf
          server {
              listen 80;
              listen 443 ssl;
              server_name mapi.zn.nextv.show;
          
              access_log  /var/log/nginx/mapi.zn.nextv.access.log;
              error_log  /var/log/nginx/mapi.zn.nextv.error.log;
          
              ssl_certificate   /etc/letsencrypt/live/zn.nextv.show/fullchain.pem;
              ssl_certificate_key  /etc/letsencrypt/live/zn.nextv.show/privkey.pem;
              ssl_session_timeout 5m;
              ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
              ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
              ssl_prefer_server_ciphers on;
          
              location / {
                  proxy_pass         http://127.0.0.1:{{PORT}}$request_uri;
                  proxy_set_header   Host \$host;
                  proxy_set_header   X-Real-IP \$remote_addr;
                  proxy_set_header   X-Forwarded-For \$proxy_add_x_forwarded_for;
                  proxy_set_header   X-Forwarded-Proto \$scheme;
                  proxy_set_header   X-Original-URI \$request_uri;
                  proxy_cache_bypass \$http_upgrade;
                  client_max_body_size  300m;
              }
          }
          EOF
          echo "Nginx configuration template created"
      
      # 步骤3: 精确停止当前应用（不影响其他实例）
      - name: Stop current application only
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mapi"
            APP_ID="${{ env.APP_ID }}"
            
            echo "Stopping ONLY current application: $APP_ID"
            
            # 方法1: 通过部署目录精准定位
            echo "Method 1: Stopping by deployment directory"
            DIR_PIDS=$(ps -ef | grep java | grep "$APP_DIR/source-admin.jar" | grep -v grep | awk '{print $2}' || true)
            
            if [ -n "$DIR_PIDS" ]; then
              echo "Found processes by directory: $DIR_PIDS"
              kill -9 $DIR_PIDS
              echo "Stopped processes by directory"
            else
              echo "No processes found by directory"
            fi
            
            # 方法2: 通过应用ID精准定位（双重保障）
            echo "Method 2: Stopping by application ID"
            ID_PIDS=$(ps -ef | grep java | grep "app.id=$APP_ID" | grep -v grep | awk '{print $2}' || true)
            
            if [ -n "$ID_PIDS" ]; then
              echo "Found processes by ID: $ID_PIDS"
              kill -9 $ID_PIDS
              echo "Stopped processes by ID"
            else
              echo "No processes found by ID"
            fi
            
            # 确保进程已停止
            sleep 2
            echo "Final check for application processes:"
            ps -ef | grep java | grep -e "$APP_DIR/source-admin.jar" -e "app.id=$APP_ID" | grep -v grep || echo "No processes found"

      - name: Prepare target directory and upload JAR
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mapi"
            mkdir -p "$APP_DIR"
            if [ -f "$APP_DIR/source-admin.jar" ]; then
              mv "$APP_DIR/source-admin.jar" "$APP_DIR/source-admin.jar.backup.$(date +%Y%m%d_%H%M%S)"
              echo "Existing JAR backed up"
            fi

      - name: Upload JAR file via SCP
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "source-admin/target/source-admin.jar"
          target: "/www/zn/zn.mapi/"
          overwrite: true
          strip_components: 2

      # 步骤4: 使用动态端口和唯一标识启动服务
      - name: Start service with unique identity
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mapi"
            APP_ID="${{ env.APP_ID }}"
            cd "$APP_DIR"
            
            # 使用生成的动态端口
            DYNAMIC_PORT=${{ steps.port-generator.outputs.port }}
            echo "Starting service with dynamic port: $DYNAMIC_PORT"
            
            # 启动服务时添加唯一标识参数
            nohup java -jar -Duser.timezone=Asia/Shanghai -Xms32M -Xmx64M \
                -Dapp.id=$APP_ID \
                source-admin.jar --server.port=$DYNAMIC_PORT > mapi.log 2>&1 &
            
            # 记录启动的PID
            JAVA_PID=$!
            echo "Java process started with PID: $JAVA_PID"
            echo "Service started on port: $DYNAMIC_PORT"
            echo "Application ID: $APP_ID"
            
            # 将端口写入文件，供后续步骤使用
            echo $DYNAMIC_PORT > current_port.txt
            
            # 等待服务启动
            sleep 15
            echo "Service startup logs:"
            tail -20 mapi.log

      # 步骤5: 更新Nginx配置
      - name: Update Nginx configuration
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mapi"
            cd "$APP_DIR"
            
            # 获取当前使用的端口
            CURRENT_PORT=$(cat current_port.txt)
            echo "Updating Nginx to use port: $CURRENT_PORT"
            
            # 创建新的Nginx配置
            sudo tee /etc/nginx/conf.d/mapi.zn.nextv.show.conf << EOF
            server {
                listen 80;
                listen 443 ssl;
                server_name mapi.zn.nextv.show;
            
                access_log  /var/log/nginx/mapi.zn.nextv.access.log;
                error_log  /var/log/nginx/mapi.zn.nextv.error.log;
            
                ssl_certificate   /etc/letsencrypt/live/zn.nextv.show/fullchain.pem;
                ssl_certificate_key  /etc/letsencrypt/live/zn.nextv.show/privkey.pem;
                ssl_session_timeout 5m;
                ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
                ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
                ssl_prefer_server_ciphers on;
            
                location / {
                    proxy_pass         http://127.0.0.1:$CURRENT_PORT\$request_uri;
                    proxy_set_header   Host \$host;
                    proxy_set_header   X-Real-IP \$remote_addr;
                    proxy_set_header   X-Forwarded-For \$proxy_add_x_forwarded_for;
                    proxy_set_header   X-Forwarded-Proto \$scheme;
                    proxy_set_header   X-Original-URI \$request_uri;
                    proxy_cache_bypass \$http_upgrade;
                    client_max_body_size  300m;
                }
            }
            EOF
            
            # 测试并重载Nginx
            sudo nginx -t
            sudo systemctl reload nginx
            echo "Nginx configuration updated successfully with port: $CURRENT_PORT"

      # 步骤6: 部署后验证
      - name: Post-deployment verification
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          port: ${{ secrets.SSH_PORT }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -euo pipefail
            APP_DIR="/www/zn/zn.mapi"
            APP_ID="${{ env.APP_ID }}"
            cd "$APP_DIR"
            
            # 获取当前使用的端口
            CURRENT_PORT=$(cat current_port.txt)
            echo "Verifying service on port: $CURRENT_PORT"
            
            # 检查服务状态（通过应用ID精确匹配）
            PID=$(ps -ef | grep java | grep "app.id=$APP_ID" | grep -v grep | awk '{print $2}' || true)
            if [ -n "$PID" ]; then
              echo "✅ Service is running with PID: $PID"
              
              # 健康检查
              HEALTH_URL="http://localhost:$CURRENT_PORT/actuator/health"
              echo "Performing health check at: $HEALTH_URL"
              
              RESPONSE_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL" || true)
              
              if [ "$RESPONSE_CODE" == "200" ]; then
                echo "✅ Health check successful (HTTP 200)"
              else
                echo "❌ Health check failed (HTTP code: $RESPONSE_CODE)"
                echo "📋 Latest application logs:"
                tail -50 mapi.log
                exit 1
              fi
            else
              echo "❌ Service verification failed - process not found"
              echo "📋 All Java processes:"
              ps -ef | grep java | grep -v grep
              echo "📋 Full log content:"
              cat mapi.log
              exit 1
            fi
            
            echo "🎉 Deployment and verification completed successfully!"