server {
    listen 80;
    listen 443 ssl;
    server_name mapi.zn.nextv.show;

    access_log  /var/log/nginx/mapi.zn.nextv.access.log;
    error_log  /var/log/nginx/mapi.zn.nextv.error.log;

    ssl_certificate   /etc/letsencrypt/live/zn.nextv.show/fullchain.pem;
    ssl_certificate_key  /etc/letsencrypt/live/zn.nextv.show/privkey.pem;
    ssl_session_timeout 5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_prefer_server_ciphers on;


    location / {
        proxy_pass         http://127.0.0.1:{{PORT}}$request_uri;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Proto $scheme;
        proxy_set_header   X-Original-URI $request_uri;
        proxy_cache_bypass $http_upgrade;
        client_max_body_size  300m;
    }
}
